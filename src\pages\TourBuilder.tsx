
import React, { useState, useEffect } from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar as CalendarIcon, Home, Loader2, Star, Sparkles } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import { FirebaseService } from '@/services/firebase';
import { EmailService, CustomTourEmailData } from '@/services/emailService';
import { DestinationService } from '@/services/destinationService';
import { Destination } from '@/types/firebase';
import { format } from 'date-fns';
import '@/styles/tour-builder-luxury.css';

const TourBuilder = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [destinations, setDestinations] = useState<Destination[]>([]);
  const [destinationsLoading, setDestinationsLoading] = useState(true);
  const [destinationsError, setDestinationsError] = useState<string | null>(null);

  const [tourData, setTourData] = useState({
    // Basic Info
    duration: 5,
    participants: 6,
    budget: [4000],
    startDate: null as Date | null,

    // Destinations
    destinations: [] as string[],

    // Interests
    interests: [] as string[],

    // Accommodation
    accommodation: 'midrange' as 'budget' | 'midrange' | 'luxury' | 'ultra',

    // Activities
    activities: [] as string[],

    // Special Requirements
    specialRequests: '',
    fitnessLevel: 'moderate' as 'easy' | 'moderate' | 'challenging',
    photographyInterest: false,

    // Contact Info
    name: '',
    email: '',
    phone: ''
  });

  // Fetch destinations from Firebase
  useEffect(() => {
    const fetchDestinations = async () => {
      try {
        setDestinationsLoading(true);
        setDestinationsError(null);
        const fetchedDestinations = await DestinationService.getDestinations();
        setDestinations(fetchedDestinations);
      } catch (error) {
        console.error('Error fetching destinations:', error);
        setDestinationsError('Failed to load destinations. Please try again.');
        // Fallback to hardcoded destinations if Firebase fails
        const fallbackDestinations: Destination[] = [
          'SERENGETI', 'NGORONGORO', 'TARANGIRE', 'MIKUMI', 'KILIMANJARO',
          'ZANZIBAR', 'MERU', 'USAMBARA', 'RUAHA'
        ].map((name, index) => ({
          id: `fallback-${index}`,
          name: name,
          description: '',
          country: 'Tanzania',
          region: '',
          coordinates: { lat: 0, lng: 0 },
          bestTimeToVisit: [],
          climate: '',
          wildlife: [],
          images: [],
          activities: [],
          accommodations: [],
          featured: false,
          detailedGuide: {
            overview: '',
            geography: '',
            history: '',
            bestTimeToVisit: {
              drySeason: '',
              greenSeason: '',
              photography: '',
              birding: ''
            },
            gettingThere: '',
            accommodation: '',
            packingTips: [],
            healthSafety: '',
            travelTips: []
          },
          seasonalInfo: {
            drySeason: {
              months: [],
              description: '',
              wildlife: '',
              photography: '',
              advantages: [],
              disadvantages: []
            },
            greenSeason: {
              months: [],
              description: '',
              wildlife: '',
              photography: '',
              advantages: [],
              disadvantages: []
            }
          },
          conservationInfo: {
            initiatives: [],
            challenges: [],
            howTouristsHelp: [],
            conservationFee: 0
          },
          culturalInfo: {
            tribes: [],
            languages: [],
            traditions: [],
            etiquette: [],
            culturalSites: []
          },
          createdAt: new Date() as any,
          updatedAt: new Date() as any
        }));
        setDestinations(fallbackDestinations);
      } finally {
        setDestinationsLoading(false);
      }
    };

    fetchDestinations();
  }, []);

  const interests = [
    'Big Five Safari',
    'Great Migration',
    'Bird Watching',
    'Photography',
    'Cultural Experiences',
    'Adventure Sports',
    'Relaxation',
    'Conservation Learning'
  ];

  const activities = [
    'Game Drives',
    'Walking Safaris',
    'Hot Air Balloon',
    'Cultural Village Visits',
    'Night Drives',
    'Bush Camping',
    'Photography Workshops',
    'Conservation Activities'
  ];

  const handleArrayToggle = (array: string[], item: string, field: keyof typeof tourData) => {
    const newArray = array.includes(item)
      ? array.filter(i => i !== item)
      : [...array, item];
    
    setTourData(prev => ({
      ...prev,
      [field]: newArray
    }));
  };

  // Helper function to send custom tour email notification
  const sendCustomTourEmailNotification = async (tourData: any, requestId: string) => {
    try {
      const emailData: CustomTourEmailData = {
        customerName: tourData.name,
        customerEmail: tourData.email,
        customerPhone: tourData.phone,
        duration: tourData.duration,
        participants: tourData.participants,
        budget: tourData.budget,
        startDate: tourData.startDate ? format(tourData.startDate, 'yyyy-MM-dd') : '',
        destinations: tourData.destinations,
        interests: tourData.interests,
        accommodation: tourData.accommodation,
        activities: tourData.activities,
        specialRequests: tourData.specialRequests,
        fitnessLevel: tourData.fitnessLevel,
        photographyInterest: tourData.photographyInterest,
        requestId
      };

      await EmailService.sendCustomTourNotification(emailData);
      console.log('✅ Custom tour email notification sent successfully');
    } catch (error) {
      console.error('❌ Error sending custom tour email notification:', error);
      // Don't throw error to avoid breaking the submission process
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!tourData.name || !tourData.email) {
      toast({
        title: "Please fill in your contact information",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);
    try {
      // Send the tour request to Firebase
      const requestId = await FirebaseService.createCustomTourRequest({
        duration: tourData.duration,
        participants: tourData.participants,
        budget: tourData.budget,
        startDate: tourData.startDate ? format(tourData.startDate, 'yyyy-MM-dd') : '',
        destinations: tourData.destinations,
        interests: tourData.interests,
        accommodation: tourData.accommodation,
        activities: tourData.activities,
        specialRequests: tourData.specialRequests,
        fitnessLevel: tourData.fitnessLevel,
        photographyInterest: tourData.photographyInterest,
        name: tourData.name,
        email: tourData.email,
        phone: tourData.phone
      });

      // Send email notification to admin
      await sendCustomTourEmailNotification(tourData, requestId);

      toast({
        title: "Custom Tour Request Sent!",
        description: "We'll contact you within 24 hours with a personalized itinerary."
      });

      // Reset form
      setTourData({
        duration: 5,
        participants: 6,
        budget: [4000],
        startDate: null,
        destinations: [],
        interests: [],
        accommodation: 'midrange',
        activities: [],
        specialRequests: '',
        fitnessLevel: 'moderate',
        photographyInterest: false,
        name: '',
        email: '',
        phone: ''
      });

    } catch (error) {
      console.error('Error sending custom tour request:', error);
      toast({
        title: "Failed to send request",
        description: "Please try again or contact us directly.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Luxury section separator component
  const LuxurySectionSeparator = ({ title }: { title: string }) => (
    <div className="relative my-8 md:my-12">
      <div className="flex items-center justify-center">
        <div className="flex-1 h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/30 to-transparent"></div>
        <div className="mx-4 md:mx-6">
          <div className="bg-[#D4C2A4]/10 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-full px-4 md:px-6 py-2 md:py-3">
            <div className="flex items-center gap-2">
              <Star className="w-3 h-3 md:w-4 md:h-4 text-[#D4C2A4]" />
              <span className="font-open-sans text-xs md:text-sm text-[#D4C2A4] tracking-wider uppercase font-medium">
                {title}
              </span>
              <Star className="w-3 h-3 md:w-4 md:h-4 text-[#D4C2A4]" />
            </div>
          </div>
        </div>
        <div className="flex-1 h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/30 to-transparent"></div>
      </div>
    </div>
  );

  return (
    <div
      className="min-h-screen relative"
      style={{
        backgroundImage: 'url("https://firebasestorage.googleapis.com/v0/b/warriorsofafricasafari-5bb0d.firebasestorage.app/o/imagez%2FIMG_3272.webp?alt=media&token=7105244d-a662-45f3-94ca-d994a1bccfb2")',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}
    >
      {/* Background overlay */}
      <div className="absolute inset-0 bg-black bg-opacity-60"></div>

      {/* Home Icon - Top Left */}
      <div className="absolute top-6 left-6 z-20">
        <button
          onClick={() => navigate('/')}
          className="flex items-center justify-center w-12 h-12 bg-[#D4C2A4]/20 backdrop-blur-sm border border-[#D4C2A4]/30 rounded-full shadow-lg hover:bg-[#D4C2A4]/30 hover:shadow-xl transition-all duration-300 group"
          aria-label="Go to Homepage"
        >
          <Home className="w-5 h-5 text-[#F2EEE6] group-hover:text-[#D4C2A4] transition-colors" />
        </button>
      </div>

      {/* Content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center p-4 py-12">
        <div className="w-full max-w-4xl">
          {/* Luxury Header */}
          <div className="text-center mb-8 md:mb-12">
            {/* Luxury Badge */}
            <div className="inline-flex items-center gap-2 bg-[#D4C2A4]/10 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-full px-4 md:px-6 py-2 mb-6">
              <Star className="w-4 h-4 text-[#D4C2A4]" />
              <span className="font-open-sans text-sm text-[#D4C2A4] tracking-wider uppercase">Premium Safari Experience</span>
            </div>

            <h1 className="font-cormorant text-4xl md:text-5xl lg:text-6xl text-[#F2EEE6] mb-4 leading-tight">
              Design Your
              <span className="block text-[#D4C2A4] italic">Dream Safari</span>
            </h1>
            <p className="font-open-sans text-[#F2EEE6]/70 text-lg md:text-xl max-w-2xl mx-auto leading-relaxed">
              Share your vision with us, and we'll craft an unforgettable safari experience tailored to your dreams.
            </p>
          </div>

          {/* Luxury Form Container */}
          <div className="tour-builder-glass rounded-2xl md:rounded-3xl p-6 md:p-8 lg:p-12 shadow-2xl max-w-4xl mx-auto">
            <form onSubmit={handleSubmit} className="space-y-8 md:space-y-12">

              {/* Section 1: Basic Information */}
              <div className="luxury-form-section">
                <div className="text-center mb-6 md:mb-8">
                  <h2 className="font-cormorant text-2xl md:text-3xl text-[#F2EEE6] mb-2">
                    Tell us about <em className="italic">yourself</em>
                  </h2>
                  <p className="font-open-sans text-[#F2EEE6]/70 text-sm">
                    Share your basic travel preferences
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8">
                  <div className="space-y-2">
                    <Label className="font-open-sans text-sm text-[#D4C2A4] tracking-wide uppercase">
                      Duration: {tourData.duration} Days
                    </Label>
                    <Slider
                      value={[tourData.duration]}
                      onValueChange={(value) => setTourData(prev => ({ ...prev, duration: value[0] }))}
                      max={14}
                      min={2}
                      step={1}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-[#F2EEE6]/50 font-open-sans">
                      <span>2 days</span>
                      <span>14 days</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="font-open-sans text-sm text-[#D4C2A4] tracking-wide uppercase">
                      Participants: {tourData.participants}
                    </Label>
                    <Slider
                      value={[tourData.participants]}
                      onValueChange={(value) => setTourData(prev => ({ ...prev, participants: value[0] }))}
                      max={12}
                      min={1}
                      step={1}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-[#F2EEE6]/50 font-open-sans">
                      <span>1 person</span>
                      <span>12 people</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="font-open-sans text-sm text-[#D4C2A4] tracking-wide uppercase">
                      Budget: ${tourData.budget[0].toLocaleString()}
                    </Label>
                    <Slider
                      value={tourData.budget}
                      onValueChange={(value) => setTourData(prev => ({ ...prev, budget: value }))}
                      max={10000}
                      min={500}
                      step={100}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-[#F2EEE6]/50 font-open-sans">
                      <span>$500</span>
                      <span>$10,000+</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="font-open-sans text-sm text-[#D4C2A4] tracking-wide uppercase">
                      Preferred Start Date
                    </Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          type="button"
                          variant="outline"
                          className="w-full justify-start text-left font-normal h-12 bg-[#F2EEE6]/5 border-[#D4C2A4]/30 text-[#F2EEE6] hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/50 rounded-lg"
                        >
                          <CalendarIcon className="mr-3 h-4 w-4 text-[#D4C2A4]" />
                          {tourData.startDate ? (
                            <span className="text-[#F2EEE6]">{format(tourData.startDate, "PPP")}</span>
                          ) : (
                            <span className="text-[#F2EEE6]/50">Pick your departure date</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0 bg-[#16191D] border border-[#D4C2A4]/30 shadow-2xl" align="start">
                        <div className="p-4 border-b border-[#D4C2A4]/20">
                          <h3 className="font-cormorant text-lg font-semibold text-[#F2EEE6] mb-1">
                            Choose Your <em className="italic">Adventure</em> Date
                          </h3>
                          <p className="text-xs text-[#F2EEE6]/70 font-open-sans">
                            Select the perfect time for your safari experience
                          </p>
                        </div>
                        <div className="p-3">
                          <Calendar
                            mode="single"
                            selected={tourData.startDate || undefined}
                            onSelect={(date) => setTourData(prev => ({ ...prev, startDate: date || null }))}
                            disabled={(date) => date < new Date()}
                            initialFocus
                            className="rounded-lg"
                          />
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
              </div>

              <LuxurySectionSeparator title="Travel Plans" />

              {/* Section 2: Destinations */}
              <div className="luxury-form-section">
                <div className="text-center mb-6 md:mb-8">
                  <h2 className="font-cormorant text-2xl md:text-3xl text-[#F2EEE6] mb-2">
                    What are your <em className="italic">Travel Plans</em>
                  </h2>
                  <p className="font-open-sans text-[#F2EEE6]/70 text-sm">
                    You can select more than one destination
                  </p>
                </div>

                {/* Destinations Grid */}
                <div className="mb-6">
                  {destinationsLoading ? (
                    <div className="flex justify-center items-center py-8">
                      <Loader2 className="h-6 w-6 animate-spin text-[#D4C2A4]" />
                      <span className="ml-2 text-sm text-[#F2EEE6]/70">Loading destinations...</span>
                    </div>
                  ) : destinationsError ? (
                    <div className="text-center py-4">
                      <p className="text-red-400 text-sm mb-2">{destinationsError}</p>
                      <button
                        type="button"
                        onClick={() => window.location.reload()}
                        className="text-[#D4C2A4] hover:text-[#D4C2A4]/80 text-sm underline"
                      >
                        Retry
                      </button>
                    </div>
                  ) : (
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 md:gap-4">
                      {destinations.map((destination) => (
                        <button
                          key={destination.id}
                          type="button"
                          onClick={() => handleArrayToggle(tourData.destinations, destination.name, 'destinations')}
                          className={`luxury-selection-button px-4 py-3 border text-sm font-open-sans transition-all duration-200 rounded-lg ${
                            tourData.destinations.includes(destination.name)
                              ? 'selected bg-[#D4C2A4]/20 text-[#F2EEE6] border-[#D4C2A4] shadow-lg'
                              : 'bg-[#F2EEE6]/5 text-[#F2EEE6]/70 border-[#D4C2A4]/30 hover:border-[#D4C2A4]/50 hover:bg-[#D4C2A4]/10'
                          }`}
                        >
                          {destination.name}
                        </button>
                      ))}
                    </div>
                  )}
                </div>

                {/* Expert Call Link */}
                <div className="text-center">
                  <a
                    href="tel:+25566121379"
                    className="inline-flex items-center gap-2 text-[#D4C2A4] hover:text-[#D4C2A4]/80 text-sm font-open-sans transition-colors"
                  >
                    <Sparkles className="w-4 h-4" />
                    Call our Expert for More Explanations
                  </a>
                </div>
              </div>

              <LuxurySectionSeparator title="Interests" />

              {/* Section 3: Interests */}
              <div className="luxury-form-section">
                <div className="text-center mb-6 md:mb-8">
                  <h2 className="font-cormorant text-2xl md:text-3xl text-[#F2EEE6] mb-2">
                    What <em className="italic">interests</em> you most?
                  </h2>
                  <p className="font-open-sans text-[#F2EEE6]/70 text-sm">
                    Select all that apply
                  </p>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 md:gap-4">
                  {interests.map((interest) => (
                    <button
                      key={interest}
                      type="button"
                      onClick={() => handleArrayToggle(tourData.interests, interest, 'interests')}
                      className={`luxury-selection-button p-4 border rounded-lg text-sm font-open-sans transition-all duration-200 ${
                        tourData.interests.includes(interest)
                          ? 'selected bg-[#D4C2A4]/20 text-[#F2EEE6] border-[#D4C2A4] shadow-lg'
                          : 'bg-[#F2EEE6]/5 text-[#F2EEE6]/70 border-[#D4C2A4]/30 hover:border-[#D4C2A4]/50 hover:bg-[#D4C2A4]/10'
                      }`}
                    >
                      {interest}
                    </button>
                  ))}
                </div>
              </div>

              <LuxurySectionSeparator title="Preferences" />

              {/* Section 4: Travel Preferences */}
              <div className="luxury-form-section">
                <div className="text-center mb-6 md:mb-8">
                  <h2 className="font-cormorant text-2xl md:text-3xl text-[#F2EEE6] mb-2">
                    Your <em className="italic">travel</em> preferences
                  </h2>
                  <p className="font-open-sans text-[#F2EEE6]/70 text-sm">
                    Help us customize your experience
                  </p>
                </div>

                <div className="space-y-6 md:space-y-8">
                  <div>
                    <Label className="font-open-sans text-sm text-[#D4C2A4] tracking-wide uppercase mb-4 block">
                      Accommodation Level
                    </Label>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      {[
                        { value: 'budget', label: 'Budget', desc: '$300-400/night' },
                        { value: 'midrange', label: 'Mid-range', desc: '$400-600/night' },
                        { value: 'luxury', label: 'Luxury', desc: '$600-1000/night' },
                        { value: 'ultra', label: 'Ultra Luxury', desc: '$1000+/night' }
                      ].map((option) => (
                        <button
                          key={option.value}
                          type="button"
                          onClick={() => setTourData(prev => ({ ...prev, accommodation: option.value as 'budget' | 'midrange' | 'luxury'| 'ultra' }))}
                          className={`luxury-selection-button p-4 border rounded-lg transition-all duration-200 ${
                            tourData.accommodation === option.value
                              ? 'selected bg-[#D4C2A4]/20 text-[#F2EEE6] border-[#D4C2A4] shadow-lg'
                              : 'bg-[#F2EEE6]/5 text-[#F2EEE6]/70 border-[#D4C2A4]/30 hover:border-[#D4C2A4]/50 hover:bg-[#D4C2A4]/10'
                          }`}
                        >
                          <div className="text-center">
                            <div className="font-semibold text-sm font-cormorant">{option.label}</div>
                            <div className="text-xs text-[#F2EEE6]/50 font-open-sans mt-1">{option.desc}</div>
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>

                  <div>
                    <Label className="font-open-sans text-sm text-[#D4C2A4] tracking-wide uppercase mb-3 block">
                      Fitness Level
                    </Label>
                    <Select
                      value={tourData.fitnessLevel}
                      onValueChange={(value) => setTourData(prev => ({ ...prev, fitnessLevel: value as 'easy' | 'moderate' | 'challenging' }))}
                    >
                      <SelectTrigger className="h-12 bg-[#F2EEE6]/5 border-[#D4C2A4]/30 text-[#F2EEE6] hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/50 rounded-lg">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="bg-[#16191D] border-[#D4C2A4]/30">
                        <SelectItem className='text-[#F2EEE6] hover:bg-[#D4C2A4]/20' value="easy">Easy - Minimal walking</SelectItem>
                        <SelectItem className='text-[#F2EEE6] hover:bg-[#D4C2A4]/20' value="moderate">Moderate - Some walking</SelectItem>
                        <SelectItem className='text-[#F2EEE6] hover:bg-[#D4C2A4]/20' value="challenging">Challenging - Extensive walking/hiking</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              <LuxurySectionSeparator title="Activities" />

              {/* Section 5: Activities */}
              <div className="luxury-form-section">
                <div className="text-center mb-6 md:mb-8">
                  <h2 className="font-cormorant text-2xl md:text-3xl text-[#F2EEE6] mb-2">
                    What <em className="italic">activities</em> interest you?
                  </h2>
                  <p className="font-open-sans text-[#F2EEE6]/70 text-sm">
                    Select all that apply
                  </p>
                </div>

                <div className="space-y-6 md:space-y-8">
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 md:gap-4">
                    {activities.map((activity) => (
                      <button
                        key={activity}
                        type="button"
                        onClick={() => handleArrayToggle(tourData.activities, activity, 'activities')}
                        className={`luxury-selection-button p-4 border rounded-lg text-sm font-open-sans transition-all duration-200 ${
                          tourData.activities.includes(activity)
                            ? 'selected bg-[#D4C2A4]/20 text-[#F2EEE6] border-[#D4C2A4] shadow-lg'
                            : 'bg-[#F2EEE6]/5 text-[#F2EEE6]/70 border-[#D4C2A4]/30 hover:border-[#D4C2A4]/50 hover:bg-[#D4C2A4]/10'
                        }`}
                      >
                        {activity}
                      </button>
                    ))}
                  </div>

                  <div>
                    <Label htmlFor="special" className="font-open-sans text-sm text-[#D4C2A4] tracking-wide uppercase mb-3 block">
                      Special Requests
                    </Label>
                    <Textarea
                      id="special"
                      value={tourData.specialRequests}
                      onChange={(e) => setTourData(prev => ({ ...prev, specialRequests: e.target.value }))}
                      placeholder="Any dietary restrictions, mobility requirements, or special requests..."
                      className="luxury-input min-h-[120px] rounded-lg font-open-sans resize-none"
                      rows={4}
                    />
                  </div>
                </div>
              </div>

              <LuxurySectionSeparator title="Contact Details" />

              {/* Section 6: Contact Information */}
              <div className="luxury-form-section">
                <div className="text-center mb-6 md:mb-8">
                  <h2 className="font-cormorant text-2xl md:text-3xl text-[#F2EEE6] mb-2">
                    Almost <em className="italic">done!</em>
                  </h2>
                  <p className="font-open-sans text-[#F2EEE6]/70 text-sm">
                    We'll contact you with a personalized itinerary
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8">
                  <div className="space-y-2">
                    <Label htmlFor="name" className="font-open-sans text-sm text-[#D4C2A4] tracking-wide uppercase">
                      Full Name *
                    </Label>
                    <Input
                      id="name"
                      value={tourData.name}
                      onChange={(e) => setTourData(prev => ({ ...prev, name: e.target.value }))}
                      required
                      className="luxury-input h-12 rounded-lg font-open-sans"
                      placeholder="Your full name"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email" className="font-open-sans text-sm text-[#D4C2A4] tracking-wide uppercase">
                      Email Address *
                    </Label>
                    <Input
                      id="email"
                      type="email"
                      value={tourData.email}
                      onChange={(e) => setTourData(prev => ({ ...prev, email: e.target.value }))}
                      required
                      className="luxury-input h-12 rounded-lg font-open-sans"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="phone" className="font-open-sans text-sm text-[#D4C2A4] tracking-wide uppercase">
                      Phone Number
                    </Label>
                    <Input
                      id="phone"
                      value={tourData.phone}
                      onChange={(e) => setTourData(prev => ({ ...prev, phone: e.target.value }))}
                      className="luxury-input h-12 rounded-lg font-open-sans"
                      placeholder="+****************"
                    />
                  </div>
                </div>
              </div>

              {/* Submit Button */}
              <div className="text-center pt-6 md:pt-8">
                <Button
                  type="submit"
                  disabled={isSubmitting || !tourData.name || !tourData.email}
                  className="luxury-submit-button text-[#16191D] font-semibold px-8 md:px-12 py-3 md:py-4 rounded-xl text-base md:text-lg disabled:opacity-50 disabled:cursor-not-allowed font-open-sans tracking-wide"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="w-5 h-5 animate-spin mr-2" />
                      Sending Request...
                    </>
                  ) : (
                    <>
                      <Sparkles className="w-5 h-5 mr-2" />
                      Send My Dream Safari Request
                    </>
                  )}
                </Button>
              </div>

            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TourBuilder;
